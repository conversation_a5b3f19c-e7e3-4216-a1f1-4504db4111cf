// Custom hook for Trading Session Management

import { useCallback, useEffect, useRef } from 'react';
import { useTradingSessionStore } from '@/stores';
import { useErrorHandler } from '@/stores/errorStore';
import { CSVParseResult, CandleData } from '@/types/trading';
import { UseTradingSessionReturn } from '@/types/stores';
import { TimeframeAggregator } from '@/utils/timeframeAggregator';
import { getBidAskFromCandle, getBidAskFromTick } from '@/utils/tradingCalculations';
import { convertTicksToCandles } from '@/utils/csvParser';

// Helper function to convert timeframe string to seconds
const getTimeframeSeconds = (timeframe: string): number => {
  const timeframeMap: Record<string, number> = {
    'S1': 1,
    'S5': 5,
    'S10': 10,
    'S15': 15,
    'S30': 30,
    'M1': 60,
    'M5': 300,
    'M15': 900,
    'M30': 1800,
    'H1': 3600,
    'H4': 14400,
    'D1': 86400,
    'W1': 604800,
    'MN1': 2592000
  };
  return timeframeMap[timeframe] || 60; // Default to M1 if unknown
};

export const useTradingSession = (): UseTradingSessionReturn => {
  const store = useTradingSessionStore();
  const errorHandler = useErrorHandler();
  const playbackIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeframeAggregatorRef = useRef<TimeframeAggregator | null>(null);

  // Data loading and processing
  const handleDataLoaded = useCallback((result: CSVParseResult) => {
    const loadingId = 'data-processing';

    try {
      errorHandler.startLoading(loadingId, 'Processing data...');

      let candleData: CandleData[] = [];
      let baseData: CandleData[] = [];

      if (result.dataType === 'candle') {
        candleData = result.candleData || [];
        baseData = candleData;

        if (candleData.length === 0) {
          errorHandler.showError(
            'No Candle Data',
            'No candle data found in the imported file'
          );
          errorHandler.stopLoading(loadingId);
          return;
        }
      } else if (result.dataType === 'tick' && result.tickData) {
        errorHandler.updateLoading(loadingId, 'Converting tick data to candles...', 25);

        try {
          errorHandler.updateLoading(loadingId, 'Generating M1 candles from ticks...', 50);

          // Convert tick data to M1 candles (60 seconds)
          baseData = convertTicksToCandles(result.tickData, 60);
          candleData = baseData;

          if (baseData.length === 0) {
            throw new Error('No candles could be generated from tick data');
          }

          // Initialize timeframe aggregator with the generated M1 candles
          timeframeAggregatorRef.current = new TimeframeAggregator(baseData);
        } catch (error) {
          errorHandler.showError(
            'Tick Data Processing Failed',
            `Failed to generate candles from tick data: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
          // Still allow chart view with empty data - user can try different approach
          candleData = [];
          baseData = [];
        }
      } else {
        errorHandler.showError(
          'Invalid Data Format',
          'No valid candle or tick data found in the imported file'
        );
        errorHandler.stopLoading(loadingId);
        return;
      }

      errorHandler.updateLoading(loadingId, 'Finalizing...', 75);

      // Set precision based on data
      const precision = result.precision || 5;

      // Update store with new data - this should always happen if we got here
      store.setData(candleData, baseData, result.tickData);
      store.setSymbol(result.symbol || 'Unknown');
      store.setDataType(result.dataType);
      store.setPrecision(precision);
      store.setPlaybackSpeed(result.dataType === 'tick' ? 100 : 1000);

      // Always transition to chart view if we have any data
      store.setShowImporter(false);

      errorHandler.stopLoading(loadingId);

      if (candleData.length > 0) {
        errorHandler.showSuccess(
          'Data Loaded Successfully',
          `Loaded ${candleData.length} ${result.dataType === 'tick' ? 'candles from tick data' : 'candles'} for ${result.symbol || 'Unknown'}`
        );
      } else {
        errorHandler.showWarning(
          'Data Loaded with Issues',
          'Data was imported but no candles could be generated. Check the data format and try again.'
        );
      }
    } catch (error) {
      errorHandler.stopLoading(loadingId);
      errorHandler.showError(
        'Data Loading Failed',
        `Failed to process imported data: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [store, errorHandler]);

  // Timeframe switching
  const switchTimeframe = useCallback((newTimeframe: string) => {
    if (!timeframeAggregatorRef.current || !store.tickData) {
      // For regular candle data, just update timeframe
      store.setTimeframe(newTimeframe as any);
      return;
    }

    // For tick data, aggregate to new timeframe using the base M1 data
    try {
      const aggregatedData = timeframeAggregatorRef.current.aggregate(newTimeframe as any);
      store.setData(aggregatedData, store.baseData, store.tickData);
      store.setTimeframe(newTimeframe as any);
    } catch (error) {
      // If aggregation fails, fall back to converting ticks directly
      try {
        const timeframeSeconds = getTimeframeSeconds(newTimeframe);
        const aggregatedData = convertTicksToCandles(store.tickData, timeframeSeconds);
        store.setData(aggregatedData, store.baseData, store.tickData);
        store.setTimeframe(newTimeframe as any);
      } catch (fallbackError) {
        // If all fails, just update timeframe without changing data
        store.setTimeframe(newTimeframe as any);
      }
    }
  }, [store]);

  // Playback control
  const startPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
    }

    store.setIsPlaying(true);

    const speed = store.playbackSpeed === 'realtime' ? 100 : store.playbackSpeed;
    
    playbackIntervalRef.current = setInterval(() => {
      const currentState = useTradingSessionStore.getState();
      const maxIndex = Math.max(0, currentState.data.length - 1);
      
      if (currentState.currentIndex >= maxIndex) {
        stopPlayback();
        return;
      }
      
      store.stepForward();
    }, speed);
  }, [store]);

  const stopPlayback = useCallback(() => {
    if (playbackIntervalRef.current) {
      clearInterval(playbackIntervalRef.current);
      playbackIntervalRef.current = null;
    }
    store.setIsPlaying(false);
  }, [store]);

  const togglePlayback = useCallback(() => {
    if (store.isPlaying) {
      stopPlayback();
    } else {
      startPlayback();
    }
  }, [store.isPlaying, startPlayback, stopPlayback]);

  // Market data updates
  const updateCurrentMarketData = useCallback(() => {
    const currentData = store.data[store.currentIndex];
    if (!currentData) return;

    let bid: number, ask: number, spread: number;

    if (store.dataType === 'tick' && store.tickData) {
      // For tick data, get bid/ask from current tick
      const currentTick = store.tickData[store.currentIndex];
      if (currentTick) {
        const bidAsk = getBidAskFromTick(currentTick);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      } else {
        // Fallback to candle data
        const bidAsk = getBidAskFromCandle(currentData);
        bid = bidAsk.bid;
        ask = bidAsk.ask;
        spread = bidAsk.spread;
      }
    } else {
      // For candle data
      const bidAsk = getBidAskFromCandle(currentData);
      bid = bidAsk.bid;
      ask = bidAsk.ask;
      spread = bidAsk.spread;
    }

    store.updateMarketData(bid, ask, spread);
  }, [store]);

  // Update market data when current index changes
  useEffect(() => {
    updateCurrentMarketData();
  }, [store.currentIndex, updateCurrentMarketData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current);
      }
    };
  }, []);

  // Computed values
  const totalItems = store.dataType === 'tick' && store.tickData 
    ? store.tickData.length 
    : store.data.length;

  const hasData = store.data.length > 0 || (store.tickData?.length || 0) > 0;

  const canStepForward = store.currentIndex < Math.max(0, totalItems - 1);
  const canStepBackward = store.currentIndex > 0;

  return {
    // State
    ...store,
    
    // Computed values
    totalItems,
    hasData,
    canStepForward,
    canStepBackward,
    
    // Actions
    handleDataLoaded,
    switchTimeframe,
    startPlayback,
    stopPlayback,
    togglePlayback,
    updateCurrentMarketData
  };
};
